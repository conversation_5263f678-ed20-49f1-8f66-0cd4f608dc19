<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="250" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
</style>
</defs>

  <defs>
    <linearGradient id="noResultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#64748b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#noResultGradient)" rx="12"/>
  <circle cx="150" cy="80" r="35" fill="none" stroke="url(#iconGradient)" stroke-width="3"/>
  <path d="M130 100 L170 140" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round"/>
  <text x="150" y="180" text-anchor="middle" class="text-modern" font-size="18" font-weight="600" fill="#334155">No Results Found</text>
  <text x="150" y="205" text-anchor="middle" class="text-modern" font-size="14" fill="#64748b">Try adjusting your search criteria</text>
</svg>